%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!28 &-6355021835264987983
Texture2D:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_Name: z__HeightGradientTextureTex{"mode":1,"colorKeys":[{"color":{"r":0.9333333373069763,"g":0.9333333373069763,"b":0.9333333373069763,"a":1.0},"time":0.2500038146972656},{"color":{"r":0.3490196168422699,"g":0.33725491166114809,"b":0.615686297416687,"a":1.0},"time":0.5000076293945313},{"color":{"r":0.9490196108818054,"g":0.32156863808631899,"b":0.572549045085907,"a":1.0},"time":0.7499961853027344},{"color":{"r":0.9960784316062927,"g":0.6274510025978088,"b":0.5882353186607361,"a":1.0},"time":1.0}],"alphaKeys":[{"alpha":1.0,"time":0.0},{"alpha":1.0,"time":1.0}]}
  m_ImageContentsHash:
    serializedVersion: 2
    Hash: 00000000000000000000000000000000
  m_IsAlphaChannelOptional: 0
  serializedVersion: 4
  m_Width: 256
  m_Height: 1
  m_CompleteImageSize: 1024
  m_MipsStripped: 0
  m_TextureFormat: 5
  m_MipCount: 1
  m_IsReadable: 1
  m_IsPreProcessed: 0
  m_IgnoreMipmapLimit: 0
  m_MipmapLimitGroupName: 
  m_StreamingMipmaps: 0
  m_StreamingMipmapsPriority: 0
  m_VTOnly: 0
  m_AlphaIsTransparency: 0
  m_ImageCount: 1
  m_TextureDimension: 2
  m_TextureSettings:
    serializedVersion: 2
    m_FilterMode: 0
    m_Aniso: 1
    m_MipBias: 0
    m_WrapU: 1
    m_WrapV: 1
    m_WrapW: 1
  m_LightmapFormat: 0
  m_ColorSpace: 1
  m_PlatformBlob: 
  image data: 1024
  _typelessdata: ffeeeeeeffeeeeeeffeeeeeeffeeeeeeffeeeeeeffeeeeeeffeeeeeeffeeeeeeffeeeeeeffeeeeeeffeeeeeeffeeeeeeffeeeeeeffeeeeeeffeeeeeeffeeeeeeffeeeeeeffeeeeeeffeeeeeeffeeeeeeffeeeeeeffeeeeeeffeeeeeeffeeeeeeffeeeeeeffeeeeeeffeeeeeeffeeeeeeffeeeeeeffeeeeeeffeeeeeeffeeeeeeffeeeeeeffeeeeeeffeeeeeeffeeeeeeffeeeeeeffeeeeeeffeeeeeeffeeeeeeffeeeeeeffeeeeeeffeeeeeeffeeeeeeffeeeeeeffeeeeeeffeeeeeeffeeeeeeffeeeeeeffeeeeeeffeeeeeeffeeeeeeffeeeeeeffeeeeeeffeeeeeeffeeeeeeffeeeeeeffeeeeeeffeeeeeeffeeeeeeffeeeeeeffeeeeeeffeeeeeeffeeeeeeff59569dff59569dff59569dff59569dff59569dff59569dff59569dff59569dff59569dff59569dff59569dff59569dff59569dff59569dff59569dff59569dff59569dff59569dff59569dff59569dff59569dff59569dff59569dff59569dff59569dff59569dff59569dff59569dff59569dff59569dff59569dff59569dff59569dff59569dff59569dff59569dff59569dff59569dff59569dff59569dff59569dff59569dff59569dff59569dff59569dff59569dff59569dff59569dff59569dff59569dff59569dff59569dff59569dff59569dff59569dff59569dff59569dff59569dff59569dff59569dff59569dff59569dff59569dff59569dfff25292fff25292fff25292fff25292fff25292fff25292fff25292fff25292fff25292fff25292fff25292fff25292fff25292fff25292fff25292fff25292fff25292fff25292fff25292fff25292fff25292fff25292fff25292fff25292fff25292fff25292fff25292fff25292fff25292fff25292fff25292fff25292fff25292fff25292fff25292fff25292fff25292fff25292fff25292fff25292fff25292fff25292fff25292fff25292fff25292fff25292fff25292fff25292fff25292fff25292fff25292fff25292fff25292fff25292fff25292fff25292fff25292fff25292fff25292fff25292fff25292fff25292fff25292fff25292fffea096fffea096fffea096fffea096fffea096fffea096fffea096fffea096fffea096fffea096fffea096fffea096fffea096fffea096fffea096fffea096fffea096fffea096fffea096fffea096fffea096fffea096fffea096fffea096fffea096fffea096fffea096fffea096fffea096fffea096fffea096fffea096fffea096fffea096fffea096fffea096fffea096fffea096fffea096fffea096fffea096fffea096fffea096fffea096fffea096fffea096fffea096fffea096fffea096fffea096fffea096fffea096fffea096fffea096fffea096fffea096fffea096fffea096fffea096fffea096fffea096fffea096fffea096fffea096
  m_StreamData:
    serializedVersion: 2
    offset: 0
    size: 0
    path: 
--- !u!28 &-3903479665328065482
Texture2D:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_Name: z___Gradient_ShadingTex{"mode":0,"colorKeys":[{"color":{"r":0.4901960790157318,"g":0.9162412285804749,"b":0.9607843160629273,"a":1.0},"time":0.6868085861206055},{"color":{"r":0.9622641801834106,"g":0.0,"b":0.14344313740730287,"a":1.0},"time":0.9340657591819763}],"alphaKeys":[{"alpha":1.0,"time":0.0},{"alpha":1.0,"time":1.0}]}
  m_ImageContentsHash:
    serializedVersion: 2
    Hash: 00000000000000000000000000000000
  m_IsAlphaChannelOptional: 0
  serializedVersion: 4
  m_Width: 256
  m_Height: 1
  m_CompleteImageSize: 1024
  m_MipsStripped: 0
  m_TextureFormat: 5
  m_MipCount: 1
  m_IsReadable: 1
  m_IsPreProcessed: 0
  m_IgnoreMipmapLimit: 0
  m_MipmapLimitGroupName: 
  m_StreamingMipmaps: 0
  m_StreamingMipmapsPriority: 0
  m_VTOnly: 0
  m_AlphaIsTransparency: 0
  m_ImageCount: 1
  m_TextureDimension: 2
  m_TextureSettings:
    serializedVersion: 2
    m_FilterMode: 1
    m_Aniso: 1
    m_MipBias: 0
    m_WrapU: 1
    m_WrapV: 1
    m_WrapW: 1
  m_LightmapFormat: 0
  m_ColorSpace: 1
  m_PlatformBlob: 
  image data: 1024
  _typelessdata: ff7deaf5ff7deaf5ff7deaf5ff7deaf5ff7deaf5ff7deaf5ff7deaf5ff7deaf5ff7deaf5ff7deaf5ff7deaf5ff7deaf5ff7deaf5ff7deaf5ff7deaf5ff7deaf5ff7deaf5ff7deaf5ff7deaf5ff7deaf5ff7deaf5ff7deaf5ff7deaf5ff7deaf5ff7deaf5ff7deaf5ff7deaf5ff7deaf5ff7deaf5ff7deaf5ff7deaf5ff7deaf5ff7deaf5ff7deaf5ff7deaf5ff7deaf5ff7deaf5ff7deaf5ff7deaf5ff7deaf5ff7deaf5ff7deaf5ff7deaf5ff7deaf5ff7deaf5ff7deaf5ff7deaf5ff7deaf5ff7deaf5ff7deaf5ff7deaf5ff7deaf5ff7deaf5ff7deaf5ff7deaf5ff7deaf5ff7deaf5ff7deaf5ff7deaf5ff7deaf5ff7deaf5ff7deaf5ff7deaf5ff7deaf5ff7deaf5ff7deaf5ff7deaf5ff7deaf5ff7deaf5ff7deaf5ff7deaf5ff7deaf5ff7deaf5ff7deaf5ff7deaf5ff7deaf5ff7deaf5ff7deaf5ff7deaf5ff7deaf5ff7deaf5ff7deaf5ff7deaf5ff7deaf5ff7deaf5ff7deaf5ff7deaf5ff7deaf5ff7deaf5ff7deaf5ff7deaf5ff7deaf5ff7deaf5ff7deaf5ff7deaf5ff7deaf5ff7deaf5ff7deaf5ff7deaf5ff7deaf5ff7deaf5ff7deaf5ff7deaf5ff7deaf5ff7deaf5ff7deaf5ff7deaf5ff7deaf5ff7deaf5ff7deaf5ff7deaf5ff7deaf5ff7deaf5ff7deaf5ff7deaf5ff7deaf5ff7deaf5ff7deaf5ff7deaf5ff7deaf5ff7deaf5ff7deaf5ff7deaf5ff7deaf5ff7deaf5ff7deaf5ff7deaf5ff7deaf5ff7deaf5ff7deaf5ff7deaf5ff7deaf5ff7deaf5ff7deaf5ff7deaf5ff7deaf5ff7deaf5ff7deaf5ff7deaf5ff7deaf5ff7deaf5ff7deaf5ff7deaf5ff7deaf5ff7deaf5ff7deaf5ff7deaf5ff7deaf5ff7deaf5ff7deaf5ff7deaf5ff7deaf5ff7deaf5ff7deaf5ff7deaf5ff7deaf5ff7deaf5ff7deaf5ff7deaf5ff7deaf5ff7deaf5ff7deaf5ff7deaf5ff7deaf5ff7deaf5ff7deaf5ff7deaf5ff7deaf5ff7deaf5ff7deaf5ff7deaf5ff7deaf5ff7deaf5ff7deaf5ff7deaf5ff7deaf5ff7fe6f2ff81e3efff82dfecff84dbe8ff86d8e5ff88d4e2ff8ad0deff8ccddbff8ec9d8ff90c5d4ff92c1d1ff94beceff96bacaff97b6c7ff99b3c4ff9bafc1ff9dabbdff9fa7baffa1a4b7ffa3a0b3ffa59cb0ffa799adffa995a9ffab91a6ffac8ea3ffae8aa0ffb0869cffb28299ffb47f96ffb67b92ffb8778fffba748cffbc7088ffbe6c85ffc06882ffc1657effc3617bffc55d78ffc75a75ffc95671ffcb526effcd4f6bffcf4b67ffd14764ffd34361ffd5405dffd63c5affd83857ffda3553ffdc3150ffde2d4dffe0294affe22646ffe42243ffe61e40ffe81b3cffea1739ffeb1336ffed1032ffef0c2ffff1082cfff30429fff50125fff50025fff50025fff50025fff50025fff50025fff50025fff50025fff50025fff50025fff50025fff50025fff50025fff50025fff50025fff50025fff50025fff50025
  m_StreamData:
    serializedVersion: 2
    offset: 0
    size: 0
    path: 
--- !u!28 &-2360122557163156353
Texture2D:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_Name: z__ShadingGradientTextureTex{"mode":0,"colorKeys":[{"color":{"r":0.05931825935840607,"g":0.40566039085388186,"b":0.40566039085388186,"a":1.0},"time":0.24451056122779847},{"color":{"r":0.7876468300819397,"g":0.8664597272872925,"b":0.9433962106704712,"a":1.0},"time":0.7554894089698792}],"alphaKeys":[{"alpha":1.0,"time":0.0},{"alpha":1.0,"time":1.0}]}
  m_ImageContentsHash:
    serializedVersion: 2
    Hash: 00000000000000000000000000000000
  m_IsAlphaChannelOptional: 0
  serializedVersion: 4
  m_Width: 1024
  m_Height: 1
  m_CompleteImageSize: 8192
  m_MipsStripped: 0
  m_TextureFormat: 17
  m_MipCount: 1
  m_IsReadable: 1
  m_IsPreProcessed: 0
  m_IgnoreMipmapLimit: 0
  m_MipmapLimitGroupName: 
  m_StreamingMipmaps: 0
  m_StreamingMipmapsPriority: 0
  m_VTOnly: 0
  m_AlphaIsTransparency: 0
  m_ImageCount: 1
  m_TextureDimension: 2
  m_TextureSettings:
    serializedVersion: 2
    m_FilterMode: 1
    m_Aniso: 1
    m_MipBias: 0
    m_WrapU: 1
    m_WrapV: 1
    m_WrapW: 1
  m_LightmapFormat: 0
  m_ColorSpace: 0
  m_PlatformBlob: 
  image data: 8192
  _typelessdata: 982b7e367e36003c982b7e367e36003c982b7e367e36003c982b7e367e36003c982b7e367e36003c982b7e367e36003c982b7e367e36003c982b7e367e36003c982b7e367e36003c982b7e367e36003c982b7e367e36003c982b7e367e36003c982b7e367e36003c982b7e367e36003c982b7e367e36003c982b7e367e36003c982b7e367e36003c982b7e367e36003c982b7e367e36003c982b7e367e36003c982b7e367e36003c982b7e367e36003c982b7e367e36003c982b7e367e36003c982b7e367e36003c982b7e367e36003c982b7e367e36003c982b7e367e36003c982b7e367e36003c982b7e367e36003c982b7e367e36003c982b7e367e36003c982b7e367e36003c982b7e367e36003c982b7e367e36003c982b7e367e36003c982b7e367e36003c982b7e367e36003c982b7e367e36003c982b7e367e36003c982b7e367e36003c982b7e367e36003c982b7e367e36003c982b7e367e36003c982b7e367e36003c982b7e367e36003c982b7e367e36003c982b7e367e36003c982b7e367e36003c982b7e367e36003c982b7e367e36003c982b7e367e36003c982b7e367e36003c982b7e367e36003c982b7e367e36003c982b7e367e36003c982b7e367e36003c982b7e367e36003c982b7e367e36003c982b7e367e36003c982b7e367e36003c982b7e367e36003c982b7e367e36003c982b7e367e36003c982b7e367e36003c982b7e367e36003c982b7e367e36003c982b7e367e36003c982b7e367e36003c982b7e367e36003c982b7e367e36003c982b7e367e36003c982b7e367e36003c982b7e367e36003c982b7e367e36003c982b7e367e36003c982b7e367e36003c982b7e367e36003c982b7e367e36003c982b7e367e36003c982b7e367e36003c982b7e367e36003c982b7e367e36003c982b7e367e36003c982b7e367e36003c982b7e367e36003c982b7e367e36003c982b7e367e36003c982b7e367e36003c982b7e367e36003c982b7e367e36003c982b7e367e36003c982b7e367e36003c982b7e367e36003c982b7e367e36003c982b7e367e36003c982b7e367e36003c982b7e367e36003c982b7e367e36003c982b7e367e36003c982b7e367e36003c982b7e367e36003c982b7e367e36003c982b7e367e36003c982b7e367e36003c982b7e367e36003c982b7e367e36003c982b7e367e36003c982b7e367e36003c982b7e367e36003c982b7e367e36003c982b7e367e36003c982b7e367e36003c982b7e367e36003c982b7e367e36003c982b7e367e36003c982b7e367e36003c982b7e367e36003c982b7e367e36003c982b7e367e36003c982b7e367e36003c982b7e367e36003c982b7e367e36003c982b7e367e36003c982b7e367e36003c982b7e367e36003c982b7e367e36003c982b7e367e36003c982b7e367e36003c982b7e367e36003c982b7e367e36003c982b7e367e36003c982b7e367e36003c982b7e367e36003c982b7e367e36003c982b7e367e36003c982b7e367e36003c982b7e367e36003c982b7e367e36003c982b7e367e36003c982b7e367e36003c982b7e367e36003c982b7e367e36003c982b7e367e36003c982b7e367e36003c982b7e367e36003c982b7e367e36003c982b7e367e36003c982b7e367e36003c982b7e367e36003c982b7e367e36003c982b7e367e36003c982b7e367e36003c982b7e367e36003c982b7e367e36003c982b7e367e36003c982b7e367e36003c982b7e367e36003c982b7e367e36003c982b7e367e36003c982b7e367e36003c982b7e367e36003c982b7e367e36003c982b7e367e36003c982b7e367e36003c982b7e367e36003c982b7e367e36003c982b7e367e36003c982b7e367e36003c982b7e367e36003c982b7e367e36003c982b7e367e36003c982b7e367e36003c982b7e367e36003c982b7e367e36003c982b7e367e36003c982b7e367e36003c982b7e367e36003c982b7e367e36003c982b7e367e36003c982b7e367e36003c982b7e367e36003c982b7e367e36003c982b7e367e36003c982b7e367e36003c982b7e367e36003c982b7e367e36003c982b7e367e36003c982b7e367e36003c982b7e367e36003c982b7e367e36003c982b7e367e36003c982b7e367e36003c982b7e367e36003c982b7e367e36003c982b7e367e36003c982b7e367e36003c982b7e367e36003c982b7e367e36003c982b7e367e36003c982b7e367e36003c982b7e367e36003c982b7e367e36003c982b7e367e36003c982b7e367e36003c982b7e367e36003c982b7e367e36003c982b7e367e36003c982b7e367e36003c982b7e367e36003c982b7e367e36003c982b7e367e36003c982b7e367e36003c982b7e367e36003c982b7e367e36003c982b7e367e36003c982b7e367e36003c982b7e367e36003c982b7e367e36003c982b7e367e36003c982b7e367e36003c982b7e367e36003c982b7e367e36003c982b7e367e36003c982b7e367e36003c982b7e367e36003c982b7e367e36003c982b7e367e36003c982b7e367e36003c982b7e367e36003c982b7e367e36003c982b7e367e36003c982b7e367e36003c982b7e367e36003c982b7e367e36003c982b7e367e36003c982b7e367e36003c982b7e367e36003c982b7e367e36003c982b7e367e36003c982b7e367e36003c982b7e367e36003c982b7e367e36003c982b7e367e36003c982b7e367e36003c982b7e367e36003c982b7e367e36003c982b7e367e36003c982b7e367e36003c982b7e367e36003c982b7e367e36003cbf2b81368136003ced2b84368536003c0d2c88368a36003c242c8c368e36003c3b2c8f369236003c522c93369636003c692c96369b36003c7f2c9a369f36003c962c9e36a336003cad2ca136a736003cc42ca536ab36003cdb2ca836b036003cf22cac36b436003c082db036b836003c1f2db336bc36003c362db736c036003c4d2dba36c536003c642dbe36c936003c7b2dc236cd36003c912dc536d136003ca82dc936d636003cbf2dcd36da36003cd62dd036de36003ced2dd436e236003c042ed736e636003c1a2edb36eb36003c312edf36ef36003c482ee236f336003c5f2ee636f736003c762ee936fb36003c8c2eed360037003ca32ef1360437003cba2ef4360837003cd12ef8360c37003ce82efb361037003cff2eff361537003c152f03371937003c2c2f06371d37003c432f0a372137003c5a2f0e372637003c712f11372a37003c882f15372e37003c9e2f18373237003cb52f1c373637003ccc2f20373b37003ce32f23373f37003cfa2f27374337003c08302a374737003c14302e374b37003c1f3032375037003c2b3035375437003c363039375837003c41303c375c37003c4d3040376137003c583044376537003c643047376937003c6f304b376d37003c7a304f377137003c863052377637003c913056377a37003c9d3059377e37003ca8305d378237003cb33061378637003cbf3064378b37003cca3068378f37003cd6306b379337003ce1306f379737003ced3073379c37003cf8307637a037003c03317a37a437003c0f317d37a837003c1a318137ac37003c26318537b137003c31318837b537003c3c318c37b937003c48319037bd37003c53319337c137003c5f319737c637003c6a319a37ca37003c76319e37ce37003c8131a237d237003c8c31a537d737003c9831a937db37003ca331ac37df37003caf31b037e337003cba31b437e737003cc531b737ec37003cd131bb37f037003cdc31be37f437003ce831c237f837003cf331c637fc37003cfe31c9370038003c0a32cd370238003c1532d1370538003c2132d4370738003c2c32d8370938003c3832db370b38003c4332df370d38003c4e32e3370f38003c5a32e6371138003c6532ea371338003c7132ed371538003c7c32f1371838003c8732f5371a38003c9332f8371c38003c9e32fc371e38003caa32ff372038003cb53202382238003cc13203382438003ccc3205382638003cd73207382838003ce33209382a38003cee320b382d38003cfa320c382f38003c05330e383138003c103310383338003c1c3312383538003c273314383738003c333315383938003c3e3317383b38003c493319383d38003c55331b384038003c60331d384238003c6c331e384438003c773320384638003c833322384838003c8e3324384a38003c993326384c38003ca53327384e38003cb03329385038003cbc332b385238003cc7332d385538003cd2332f385738003cde3330385938003ce93332385b38003cf53334385d38003c003436385f38003c063438386138003c0b3439386338003c11343b386538003c17343d386838003c1d343f386a38003c223441386c38003c283443386e38003c2e3444387038003c333446387238003c393448387438003c3f344a387638003c45344c387838003c4a344d387b38003c50344f387d38003c563451387f38003c5b3453388138003c613455388338003c673456388538003c6c3458388738003c72345a388938003c78345c388b38003c7e345e388d38003c83345f389038003c893461389238003c8f3463389438003c943465389638003c9a3467389838003ca03468389a38003ca6346a389c38003cab346c389e38003cb1346e38a038003cb7347038a338003cbc347138a538003cc2347338a738003cc8347538a938003cce347738ab38003cd3347938ad38003cd9347a38af38003cdf347c38b138003ce4347e38b338003cea348038b638003cf0348238b838003cf5348438ba38003cfb348538bc38003c01358738be38003c07358938c038003c0c358b38c238003c12358d38c438003c18358e38c638003c1d359038c838003c23359238cb38003c29359438cd38003c2f359638cf38003c34359738d138003c3a359938d338003c40359b38d538003c45359d38d738003c4b359f38d938003c5135a038db38003c5635a238de38003c5c35a438e038003c6235a638e238003c6835a838e438003c6d35a938e638003c7335ab38e838003c7935ad38ea38003c7e35af38ec38003c8435b138ee38003c8a35b238f138003c9035b438f338003c9535b638f538003c9b35b838f738003ca135ba38f938003ca635bb38fb38003cac35bd38fd38003cb235bf38ff38003cb835c1380139003cbd35c3380339003cc335c5380639003cc935c6380839003cce35c8380a39003cd435ca380c39003cda35cc380e39003cdf35ce381039003ce535cf381239003ceb35d1381439003cf135d3381639003cf635d5381939003cfc35d7381b39003c0236d8381d39003c0736da381f39003c0d36dc382139003c1336de382339003c1936e0382539003c1e36e1382739003c2436e3382939003c2a36e5382b39003c2f36e7382e39003c3536e9383039003c3b36ea383239003c4036ec383439003c4636ee383639003c4c36f0383839003c5236f2383a39003c5736f3383c39003c5d36f5383e39003c6336f7384139003c6836f9384339003c6e36fb384539003c7436fc384739003c7a36fe384939003c7f3600394b39003c853602394d39003c8b3604394f39003c903606395139003c963607395439003c9c3609395639003ca1360b395839003ca7360d395a39003cad360f395c39003cb33610395e39003cb83612396039003cbe3614396239003cc43616396439003cc93618396639003ccf3619396939003cd5361b396b39003cdb361d396d39003ce0361f396f39003ce63621397139003cec3622397339003cf13624397539003cf73626397739003cfd3628397939003c03372a397c39003c08372b397e39003c0e372d398039003c14372f398239003c193731398439003c1f3733398639003c253734398839003c2a3736398a39003c303738398c39003c36373a398f39003c3c373c399139003c41373d399339003c47373f399539003c4d3741399739003c523743399939003c583745399b39003c5e3746399d39003c643748399f39003c69374a39a139003c6f374c39a439003c75374e39a639003c7a375039a839003c80375139aa39003c86375339ac39003c8b375539ae39003c91375739b039003c97375939b239003c9d375a39b439003ca2375c39b739003ca8375e39b939003cae376039bb39003cb3376239bd39003cb9376339bf39003cbf376539c139003cc5376739c339003cca376939c539003cd0376b39c739003cd6376c39ca39003cdb376e39cc39003ce1377039ce39003ce7377239d039003cec377439d239003cf2377539d439003cf8377739d639003cfe377939d839003c02387b39da39003c05387d39dc39003c07387e39df39003c0a388039e139003c0d388239e339003c10388439e539003c13388639e739003c16388739e939003c18388939eb39003c1b388b39ed39003c1e388d39ef39003c21388f39f239003c24389139f439003c27389239f639003c2a389439f839003c2c389639fa39003c2f389839fc39003c32389a39fe39003c35389b39003a003c38389d39023a003c3b389f39043a003c3e38a139073a003c4038a339093a003c4338a4390b3a003c4638a6390d3a003c4938a8390f3a003c4c38aa39113a003c4f38ac39133a003c5238ad39153a003c5438af39173a003c5738b1391a3a003c5a38b3391c3a003c5d38b5391e3a003c6038b639203a003c6338b839223a003c6638ba39243a003c6838bc39263a003c6b38be39283a003c6e38bf392a3a003c7138c1392d3a003c7438c3392f3a003c7738c539313a003c7a38c739333a003c7c38c839353a003c7f38ca39373a003c8238cc39393a003c8538ce393b3a003c8838d0393d3a003c8b38d2393f3a003c8d38d339423a003c9038d539443a003c9338d739463a003c9638d939483a003c9938db394a3a003c9c38dc394c3a003c9f38de394e3a003ca138e039503a003ca438e239523a003ca738e439553a003caa38e539573a003cad38e739593a003cb038e9395b3a003cb338eb395d3a003cb538ed395f3a003cb838ee39613a003cbb38f039633a003cbe38f239653a003cc138f439683a003cc438f6396a3a003cc738f7396c3a003cc938f9396e3a003ccc38fb39703a003ccf38fd39723a003cd238ff39743a003cd538003a763a003cd838023a783a003cdb38043a7a3a003cdd38063a7d3a003ce038083a7f3a003ce338093a813a003ce6380b3a833a003ce9380d3a853a003cec380f3a873a003cef38113a893a003cf138123a8b3a003cf438143a8d3a003cf738163a903a003cfa38183a923a003cfd381a3a943a003c00391c3a963a003c02391d3a983a003c05391f3a9a3a003c0839213a9c3a003c0b39233a9e3a003c0e39253aa03a003c1139263aa33a003c1439283aa53a003c16392a3aa73a003c19392c3aa93a003c1c392e3aab3a003c1f392f3aad3a003c2239313aaf3a003c2539333ab13a003c2839353ab33a003c2a39373ab53a003c2d39383ab83a003c30393a3aba3a003c33393c3abc3a003c36393e3abe3a003c3939403ac03a003c3c39413ac23a003c3e39433ac43a003c4139453ac63a003c4439473ac83a003c4739493acb3a003c4a394a3acd3a003c4d394c3acf3a003c50394e3ad13a003c5239503ad33a003c5539523ad53a003c5839533ad73a003c5b39553ad93a003c5e39573adb3a003c6139593add3a003c63395b3ae03a003c66395d3ae23a003c69395e3ae43a003c6c39603ae63a003c6f39623ae83a003c7239643aea3a003c7539663aec3a003c7739673aee3a003c7a39693af03a003c7d396b3af33a003c80396d3af53a003c83396f3af73a003c8639703af93a003c8939723afb3a003c8b39743afd3a003c8e39763aff3a003c9139783a013b003c9439793a033b003c97397b3a063b003c9a397d3a083b003c9d397f3a0a3b003c9f39813a0c3b003ca239823a0e3b003ca539843a103b003ca839863a123b003cab39883a143b003cae398a3a163b003cb1398b3a183b003cb3398d3a1b3b003cb6398f3a1d3b003cb939913a1f3b003cbc39933a213b003cbf39943a233b003cc239963a253b003cc539983a273b003cc7399a3a293b003cca399c3a2b3b003ccd399e3a2e3b003cd0399f3a303b003cd339a13a323b003cd639a33a343b003cd839a53a363b003cdb39a73a383b003cde39a83a3a3b003ce139aa3a3c3b003ce439ac3a3e3b003ce739ae3a413b003cea39b03a433b003cec39b13a453b003cef39b33a473b003cf239b53a493b003cf539b73a4b3b003cf839b93a4d3b003cfb39ba3a4f3b003cfe39bc3a513b003c003abe3a533b003c033ac03a563b003c063ac23a583b003c093ac33a5a3b003c0c3ac53a5c3b003c0f3ac73a5e3b003c123ac93a603b003c143acb3a623b003c173acc3a643b003c1a3ace3a663b003c1d3ad03a693b003c203ad23a6b3b003c233ad43a6d3b003c263ad53a6f3b003c283ad73a713b003c2b3ad93a733b003c2e3adb3a753b003c313add3a773b003c343adf3a793b003c373ae03a7c3b003c3a3ae23a7e3b003c3c3ae43a803b003c3f3ae63a823b003c423ae83a843b003c453ae93a863b003c483aeb3a883b003c4b3aed3a8a3b003c4d3aef3a8c3b003c4d3aef3a8c3b003c4d3aef3a8c3b003c4d3aef3a8c3b003c4d3aef3a8c3b003c4d3aef3a8c3b003c4d3aef3a8c3b003c4d3aef3a8c3b003c4d3aef3a8c3b003c4d3aef3a8c3b003c4d3aef3a8c3b003c4d3aef3a8c3b003c4d3aef3a8c3b003c4d3aef3a8c3b003c4d3aef3a8c3b003c4d3aef3a8c3b003c4d3aef3a8c3b003c4d3aef3a8c3b003c4d3aef3a8c3b003c4d3aef3a8c3b003c4d3aef3a8c3b003c4d3aef3a8c3b003c4d3aef3a8c3b003c4d3aef3a8c3b003c4d3aef3a8c3b003c4d3aef3a8c3b003c4d3aef3a8c3b003c4d3aef3a8c3b003c4d3aef3a8c3b003c4d3aef3a8c3b003c4d3aef3a8c3b003c4d3aef3a8c3b003c4d3aef3a8c3b003c4d3aef3a8c3b003c4d3aef3a8c3b003c4d3aef3a8c3b003c4d3aef3a8c3b003c4d3aef3a8c3b003c4d3aef3a8c3b003c4d3aef3a8c3b003c4d3aef3a8c3b003c4d3aef3a8c3b003c4d3aef3a8c3b003c4d3aef3a8c3b003c4d3aef3a8c3b003c4d3aef3a8c3b003c4d3aef3a8c3b003c4d3aef3a8c3b003c4d3aef3a8c3b003c4d3aef3a8c3b003c4d3aef3a8c3b003c4d3aef3a8c3b003c4d3aef3a8c3b003c4d3aef3a8c3b003c4d3aef3a8c3b003c4d3aef3a8c3b003c4d3aef3a8c3b003c4d3aef3a8c3b003c4d3aef3a8c3b003c4d3aef3a8c3b003c4d3aef3a8c3b003c4d3aef3a8c3b003c4d3aef3a8c3b003c4d3aef3a8c3b003c4d3aef3a8c3b003c4d3aef3a8c3b003c4d3aef3a8c3b003c4d3aef3a8c3b003c4d3aef3a8c3b003c4d3aef3a8c3b003c4d3aef3a8c3b003c4d3aef3a8c3b003c4d3aef3a8c3b003c4d3aef3a8c3b003c4d3aef3a8c3b003c4d3aef3a8c3b003c4d3aef3a8c3b003c4d3aef3a8c3b003c4d3aef3a8c3b003c4d3aef3a8c3b003c4d3aef3a8c3b003c4d3aef3a8c3b003c4d3aef3a8c3b003c4d3aef3a8c3b003c4d3aef3a8c3b003c4d3aef3a8c3b003c4d3aef3a8c3b003c4d3aef3a8c3b003c4d3aef3a8c3b003c4d3aef3a8c3b003c4d3aef3a8c3b003c4d3aef3a8c3b003c4d3aef3a8c3b003c4d3aef3a8c3b003c4d3aef3a8c3b003c4d3aef3a8c3b003c4d3aef3a8c3b003c4d3aef3a8c3b003c4d3aef3a8c3b003c4d3aef3a8c3b003c4d3aef3a8c3b003c4d3aef3a8c3b003c4d3aef3a8c3b003c4d3aef3a8c3b003c4d3aef3a8c3b003c4d3aef3a8c3b003c4d3aef3a8c3b003c4d3aef3a8c3b003c4d3aef3a8c3b003c4d3aef3a8c3b003c4d3aef3a8c3b003c4d3aef3a8c3b003c4d3aef3a8c3b003c4d3aef3a8c3b003c4d3aef3a8c3b003c4d3aef3a8c3b003c4d3aef3a8c3b003c4d3aef3a8c3b003c4d3aef3a8c3b003c4d3aef3a8c3b003c4d3aef3a8c3b003c4d3aef3a8c3b003c4d3aef3a8c3b003c4d3aef3a8c3b003c4d3aef3a8c3b003c4d3aef3a8c3b003c4d3aef3a8c3b003c4d3aef3a8c3b003c4d3aef3a8c3b003c4d3aef3a8c3b003c4d3aef3a8c3b003c4d3aef3a8c3b003c4d3aef3a8c3b003c4d3aef3a8c3b003c4d3aef3a8c3b003c4d3aef3a8c3b003c4d3aef3a8c3b003c4d3aef3a8c3b003c4d3aef3a8c3b003c4d3aef3a8c3b003c4d3aef3a8c3b003c4d3aef3a8c3b003c4d3aef3a8c3b003c4d3aef3a8c3b003c4d3aef3a8c3b003c4d3aef3a8c3b003c4d3aef3a8c3b003c4d3aef3a8c3b003c4d3aef3a8c3b003c4d3aef3a8c3b003c4d3aef3a8c3b003c4d3aef3a8c3b003c4d3aef3a8c3b003c4d3aef3a8c3b003c4d3aef3a8c3b003c4d3aef3a8c3b003c4d3aef3a8c3b003c4d3aef3a8c3b003c4d3aef3a8c3b003c4d3aef3a8c3b003c4d3aef3a8c3b003c4d3aef3a8c3b003c4d3aef3a8c3b003c4d3aef3a8c3b003c4d3aef3a8c3b003c4d3aef3a8c3b003c4d3aef3a8c3b003c4d3aef3a8c3b003c4d3aef3a8c3b003c4d3aef3a8c3b003c4d3aef3a8c3b003c4d3aef3a8c3b003c4d3aef3a8c3b003c4d3aef3a8c3b003c4d3aef3a8c3b003c4d3aef3a8c3b003c4d3aef3a8c3b003c4d3aef3a8c3b003c4d3aef3a8c3b003c4d3aef3a8c3b003c4d3aef3a8c3b003c4d3aef3a8c3b003c4d3aef3a8c3b003c4d3aef3a8c3b003c4d3aef3a8c3b003c4d3aef3a8c3b003c4d3aef3a8c3b003c4d3aef3a8c3b003c4d3aef3a8c3b003c4d3aef3a8c3b003c4d3aef3a8c3b003c4d3aef3a8c3b003c4d3aef3a8c3b003c4d3aef3a8c3b003c4d3aef3a8c3b003c4d3aef3a8c3b003c4d3aef3a8c3b003c4d3aef3a8c3b003c4d3aef3a8c3b003c4d3aef3a8c3b003c4d3aef3a8c3b003c4d3aef3a8c3b003c4d3aef3a8c3b003c4d3aef3a8c3b003c4d3aef3a8c3b003c4d3aef3a8c3b003c4d3aef3a8c3b003c4d3aef3a8c3b003c4d3aef3a8c3b003c4d3aef3a8c3b003c4d3aef3a8c3b003c4d3aef3a8c3b003c4d3aef3a8c3b003c4d3aef3a8c3b003c4d3aef3a8c3b003c4d3aef3a8c3b003c4d3aef3a8c3b003c4d3aef3a8c3b003c4d3aef3a8c3b003c4d3aef3a8c3b003c4d3aef3a8c3b003c4d3aef3a8c3b003c4d3aef3a8c3b003c4d3aef3a8c3b003c4d3aef3a8c3b003c4d3aef3a8c3b003c4d3aef3a8c3b003c4d3aef3a8c3b003c4d3aef3a8c3b003c4d3aef3a8c3b003c4d3aef3a8c3b003c4d3aef3a8c3b003c4d3aef3a8c3b003c4d3aef3a8c3b003c4d3aef3a8c3b003c4d3aef3a8c3b003c4d3aef3a8c3b003c4d3aef3a8c3b003c4d3aef3a8c3b003c4d3aef3a8c3b003c4d3aef3a8c3b003c4d3aef3a8c3b003c4d3aef3a8c3b003c4d3aef3a8c3b003c4d3aef3a8c3b003c4d3aef3a8c3b003c4d3aef3a8c3b003c4d3aef3a8c3b003c4d3aef3a8c3b003c4d3aef3a8c3b003c4d3aef3a8c3b003c
  m_StreamData:
    serializedVersion: 2
    offset: 0
    size: 0
    path: 
--- !u!114 &-1826353197901718966
MonoBehaviour:
  m_ObjectHideFlags: 11
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: d0353a89b1f911e48b9e16bdc9f2e058, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  version: 10
--- !u!21 &2100000
Material:
  serializedVersion: 8
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_Name: Character - Height Gradient Shader - Pullover
  m_Shader: {fileID: -6465566751694194690, guid: 30a45f146b24b654e91fbed1ffd0a015, type: 3}
  m_Parent: {fileID: 0}
  m_ModifiedSerializedProperties: 0
  m_ValidKeywords: []
  m_InvalidKeywords:
  - _BLEND_MODE_HARD_LIGHT
  - _ENABLE_HEIGHT_GRADIENT
  - _HEIGHT_MODE_1_WORLD
  - _HEIGHT_MODE_OBJECT
  m_LightmapFlags: 4
  m_EnableInstancingVariants: 0
  m_DoubleSidedGI: 0
  m_CustomRenderQueue: -1
  stringTagMap: {}
  disabledShaderPasses:
  - MOTIONVECTORS
  m_LockedProperties: 
  m_SavedProperties:
    serializedVersion: 3
    m_TexEnvs:
    - _BaseMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _BumpMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _DetailAlbedoMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _DetailMask:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _DetailNormalMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _Displacement:
        m_Texture: {fileID: 2758093736126266139, guid: 322b258e81ee14ec38a841ead32be474, type: 2}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _EmissionMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _HeightGradientTexture:
        m_Texture: {fileID: -6355021835264987983}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _MainTex:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _MetallicGlossMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _OcclusionMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _ParallaxMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _ShadingGradientTexture:
        m_Texture: {fileID: -2360122557163156353}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _SpecGlossMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - __Curve_Displacement:
        m_Texture: {fileID: -7687320413130526882, guid: 322b258e81ee14ec38a841ead32be474, type: 2}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - __Gradient_Shading:
        m_Texture: {fileID: -3903479665328065482}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - unity_Lightmaps:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - unity_LightmapsInd:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - unity_ShadowMasks:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    m_Ints: []
    m_Floats:
    - _AlphaClip: 0
    - _BLEND_MODE: 6
    - _BUILTIN_QueueControl: -1
    - _BUILTIN_QueueOffset: 0
    - _Blend: 0
    - _BumpScale: 1
    - _CastShadows: 1
    - _ClearCoatMask: 0
    - _ClearCoatSmoothness: 0
    - _Cull: 2
    - _Cutoff: 0.5
    - _DetailAlbedoMapScale: 1
    - _DetailNormalMapScale: 1
    - _DisplacementScale: 5
    - _DstBlend: 0
    - _ENABLE_HEIGHT_GRADIENT: 1
    - _EnvironmentReflections: 1
    - _GlossMapScale: 0
    - _Glossiness: 0
    - _GlossyReflections: 0
    - _HEIGHT_MODE: 0
    - _HEIGHT_MODE_1: 1
    - _Metallic: 0
    - _OcclusionStrength: 1
    - _Opacity: 0.215
    - _Parallax: 0.005
    - _QueueControl: 0
    - _QueueOffset: 0
    - _ReceiveShadows: 1
    - _Smoothness: 0.5
    - _SmoothnessTextureChannel: 0
    - _SpecularHighlights: 1
    - _SrcBlend: 1
    - _Surface: 0
    - _WorkflowMode: 1
    - _ZTest: 4
    - _ZWrite: 1
    - _ZWriteControl: 0
    m_Colors:
    - _BaseColor: {r: 0.509434, g: 0.509434, b: 0.509434, a: 1}
    - _Color: {r: 0.509434, g: 0.509434, b: 0.509434, a: 1}
    - _DisplacementRange: {r: 0, g: 2.5, b: 0, a: 0}
    - _EmissionColor: {r: 0, g: 0, b: 0, a: 1}
    - _HeightRange: {r: 0.17, g: -1.03, b: 0, a: 0}
    - _SpecColor: {r: 0.19999996, g: 0.19999996, b: 0.19999996, a: 1}
  m_BuildTextureStacks: []
  m_AllowLocking: 1
--- !u!114 &1687550147607459794
MonoBehaviour:
  m_ObjectHideFlags: 11
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 639247ca83abc874e893eb93af2b5e44, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  version: 0
