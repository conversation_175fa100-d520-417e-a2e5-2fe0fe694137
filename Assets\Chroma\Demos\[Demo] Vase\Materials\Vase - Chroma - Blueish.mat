%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!28 &-3903479665328065482
Texture2D:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_Name: z___Gradient_ShadingTex{"mode":0,"colorKeys":[{"color":{"r":0.7960784435272217,"g":0.9764705896377564,"b":0.8549019694328308,"a":1.0},"time":0.2500038146972656},{"color":{"r":0.239215686917305,"g":0.8235294222831726,"b":0.800000011920929,"a":1.0},"time":0.5000076293945313},{"color":{"r":0.24313725531101228,"g":0.41960784792900088,"b":0.5372549295425415,"a":1.0},"time":0.7499961853027344},{"color":{"r":0.07058823853731156,"g":0.1764705926179886,"b":0.25882354378700259,"a":1.0},"time":1.0}],"alphaKeys":[{"alpha":1.0,"time":0.0},{"alpha":1.0,"time":1.0}]}
  m_ImageContentsHash:
    serializedVersion: 2
    Hash: 00000000000000000000000000000000
  m_IsAlphaChannelOptional: 0
  serializedVersion: 4
  m_Width: 256
  m_Height: 1
  m_CompleteImageSize: 1024
  m_MipsStripped: 0
  m_TextureFormat: 5
  m_MipCount: 1
  m_IsReadable: 1
  m_IsPreProcessed: 0
  m_IgnoreMipmapLimit: 0
  m_MipmapLimitGroupName: 
  m_StreamingMipmaps: 0
  m_StreamingMipmapsPriority: 0
  m_VTOnly: 0
  m_AlphaIsTransparency: 0
  m_ImageCount: 1
  m_TextureDimension: 2
  m_TextureSettings:
    serializedVersion: 2
    m_FilterMode: 1
    m_Aniso: 1
    m_MipBias: 0
    m_WrapU: 1
    m_WrapV: 1
    m_WrapW: 1
  m_LightmapFormat: 0
  m_ColorSpace: 1
  m_PlatformBlob: 
  image data: 1024
  _typelessdata: ffcbf9daffcbf9daffcbf9daffcbf9daffcbf9daffcbf9daffcbf9daffcbf9daffcbf9daffcbf9daffcbf9daffcbf9daffcbf9daffcbf9daffcbf9daffcbf9daffcbf9daffcbf9daffcbf9daffcbf9daffcbf9daffcbf9daffcbf9daffcbf9daffcbf9daffcbf9daffcbf9daffcbf9daffcbf9daffcbf9daffcbf9daffcbf9daffcbf9daffcbf9daffcbf9daffcbf9daffcbf9daffcbf9daffcbf9daffcbf9daffcbf9daffcbf9daffcbf9daffcbf9daffcbf9daffcbf9daffcbf9daffcbf9daffcbf9daffcbf9daffcbf9daffcbf9daffcbf9daffcbf9daffcbf9daffcbf9daffcbf9daffcbf9daffcbf9daffcbf9daffcbf9daffcbf9daffcbf9daffcbf9daffcaf9daffc8f8daffc6f8daffc4f7d9ffc2f6d9ffbff6d9ffbdf5d9ffbbf5d8ffb9f4d8ffb6f3d8ffb4f3d8ffb2f2d8ffb0f2d7ffadf1d7ffabf0d7ffa9f0d7ffa7efd6ffa5eed6ffa2eed6ffa0edd6ff9eedd6ff9cecd5ff99ebd5ff97ebd5ff95ead5ff93ead4ff91e9d4ff8ee8d4ff8ce8d4ff8ae7d4ff88e6d3ff85e6d3ff83e5d3ff81e5d3ff7fe4d2ff7ce3d2ff7ae3d2ff78e2d2ff76e2d2ff74e1d1ff71e0d1ff6fe0d1ff6ddfd1ff6bdfd1ff68ded0ff66ddd0ff64ddd0ff62dcd0ff60dbcfff5ddbcfff5bdacfff59dacfff57d9cfff54d8ceff52d8ceff50d7ceff4ed7ceff4bd6cdff49d5cdff47d5cdff45d4cdff43d4cdff40d3ccff3ed2ccff3dd1cbff3dd0caff3dcec9ff3dccc8ff3dcbc7ff3dc9c6ff3dc8c5ff3dc6c4ff3dc4c3ff3dc3c2ff3dc1c1ff3dbfc0ff3dbebfff3dbcbeff3dbbbdff3db9bcff3db7bbff3db6baff3db4b9ff3db2b8ff3db1b6ff3dafb5ff3daeb4ff3dacb3ff3daab2ff3da9b1ff3da7b0ff3da6afff3da4aeff3da2adff3da1acff3d9fabff3e9daaff3e9ca9ff3e9aa8ff3e99a7ff3e97a6ff3e95a5ff3e94a4ff3e92a2ff3e91a1ff3e8fa0ff3e8d9fff3e8c9eff3e8a9dff3e889cff3e879bff3e859aff3e8499ff3e8298ff3e8097ff3e7f96ff3e7d95ff3e7c94ff3e7a93ff3e7892ff3e7791ff3e7590ff3e738fff3e728dff3e708cff3e6f8bff3e6d8aff3e6b89ff3d6a88ff3d6987ff3c6886ff3b6785ff3b6684ff3a6583ff396481ff396380ff38627fff37627eff37617dff36607cff355f7bff355e7aff345d79ff335c77ff325b76ff325a75ff315974ff305873ff305772ff2f5671ff2e5570ff2e546fff2d536dff2c526cff2c516bff2b506aff2a4f69ff294e68ff294d67ff284c66ff274b65ff274a63ff264962ff254861ff254760ff24465fff23455eff23445dff22435cff21425bff204159ff204058ff1f3f57ff1e3f56ff1e3e55ff1d3d54ff1c3c53ff1c3b52ff1b3a50ff1a394fff1a384eff19374dff18364cff18354bff17344aff163349ff153248ff153146ff143045ff132f44ff132e43ff122d42
  m_StreamData:
    serializedVersion: 2
    offset: 0
    size: 0
    path: 
--- !u!114 &-1826353197901718966
MonoBehaviour:
  m_ObjectHideFlags: 11
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: d0353a89b1f911e48b9e16bdc9f2e058, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  version: 10
--- !u!21 &2100000
Material:
  serializedVersion: 8
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_Name: Vase - Chroma - Blueish
  m_Shader: {fileID: -6465566751694194690, guid: 6f3f4768a2d0d0349949aa010fa89c98, type: 3}
  m_Parent: {fileID: 0}
  m_ModifiedSerializedProperties: 0
  m_ValidKeywords: []
  m_InvalidKeywords: []
  m_LightmapFlags: 4
  m_EnableInstancingVariants: 0
  m_DoubleSidedGI: 0
  m_CustomRenderQueue: -1
  stringTagMap: {}
  disabledShaderPasses:
  - MOTIONVECTORS
  m_LockedProperties: 
  m_SavedProperties:
    serializedVersion: 3
    m_TexEnvs:
    - _BaseMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _BumpMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _DetailAlbedoMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _DetailMask:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _DetailNormalMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _EmissionMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _MainTex:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _MetallicGlossMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _OcclusionMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _ParallaxMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _SpecGlossMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - __Gradient_Shading:
        m_Texture: {fileID: -3903479665328065482}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - unity_Lightmaps:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - unity_LightmapsInd:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - unity_ShadowMasks:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    m_Ints: []
    m_Floats:
    - _AlphaClip: 0
    - _Blend: 0
    - _BumpScale: 1
    - _ClearCoatMask: 0
    - _ClearCoatSmoothness: 0
    - _Cull: 2
    - _Cutoff: 0.5
    - _DetailAlbedoMapScale: 1
    - _DetailNormalMapScale: 1
    - _DstBlend: 0
    - _EnvironmentReflections: 1
    - _GlossMapScale: 0
    - _Glossiness: 0
    - _GlossyReflections: 0
    - _Metallic: 0
    - _OcclusionStrength: 1
    - _Parallax: 0.005
    - _QueueControl: 0
    - _QueueOffset: 0
    - _ReceiveShadows: 1
    - _Smoothness: 0.5
    - _SmoothnessTextureChannel: 0
    - _SpecularHighlights: 1
    - _SrcBlend: 1
    - _Surface: 0
    - _WorkflowMode: 1
    - _ZWrite: 1
    m_Colors:
    - _BaseColor: {r: 0.509434, g: 0.509434, b: 0.509434, a: 1}
    - _Color: {r: 0.509434, g: 0.509434, b: 0.509434, a: 1}
    - _EmissionColor: {r: 0, g: 0, b: 0, a: 1}
    - _SpecColor: {r: 0.19999996, g: 0.19999996, b: 0.19999996, a: 1}
  m_BuildTextureStacks: []
  m_AllowLocking: 1
