%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!114 &-8200414924709094708
MonoBehaviour:
  m_ObjectHideFlags: 11
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: d0353a89b1f911e48b9e16bdc9f2e058, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  version: 10
--- !u!21 &2100000
Material:
  serializedVersion: 8
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_Name: Ground 1
  m_Shader: {fileID: 4800000, guid: 933532a4fcc9baf4fa0491de14d08ed7, type: 3}
  m_Parent: {fileID: 0}
  m_ModifiedSerializedProperties: 0
  m_ValidKeywords: []
  m_InvalidKeywords: []
  m_LightmapFlags: 4
  m_EnableInstancingVariants: 0
  m_DoubleSidedGI: 0
  m_CustomRenderQueue: 2000
  stringTagMap:
    RenderType: Opaque
  disabledShaderPasses:
  - MOTIONVECTORS
  m_LockedProperties: 
  m_SavedProperties:
    serializedVersion: 3
    m_TexEnvs:
    - Texture2D_AC897AFA:
        m_Texture: {fileID: 10309, guid: 0000000000000000f000000000000000, type: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _BaseMap:
        m_Texture: {fileID: 10309, guid: 0000000000000000f000000000000000, type: 0}
        m_Scale: {x: 12, y: 12}
        m_Offset: {x: 0, y: 0}
    - _BumpMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _DetailAlbedoMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _DetailMask:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _DetailNormalMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _EmissionMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 12, y: 12}
        m_Offset: {x: 0, y: 0}
    - _MainTex:
        m_Texture: {fileID: 10309, guid: 0000000000000000f000000000000000, type: 0}
        m_Scale: {x: 12, y: 12}
        m_Offset: {x: 0, y: 0}
    - _MetallicGlossMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _OcclusionMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _ParallaxMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _SpecGlossMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _V_WIRE_AoMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _V_WIRE_NormalMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _V_WIRE_SourceTex:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _V_WIRE_WireTex:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - unity_Lightmaps:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - unity_LightmapsInd:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - unity_ShadowMasks:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    m_Ints: []
    m_Floats:
    - Vector1_BD6DE493: 12
    - Vector1_C691CA19: 0
    - Vector1_CF184A9E: 0.97
    - _AlphaClip: 0
    - _AlphaToMask: 0
    - _Blend: 0
    - _BlendModePreserveSpecular: 1
    - _BumpScale: 2
    - _ClearCoatMask: 0
    - _ClearCoatSmoothness: 0
    - _Cull: 2
    - _Cutoff: 0.5
    - _DetailAlbedoMapScale: 1
    - _DetailNormalMapScale: 1
    - _DstBlend: 0
    - _DstBlendAlpha: 0
    - _EnvironmentReflections: 1
    - _GlossMapScale: 1
    - _Glossiness: 0.97
    - _GlossyReflections: 1
    - _Metallic: 0
    - _Mode: 0
    - _OcclusionStrength: 1
    - _Parallax: 0.02
    - _QueueOffset: 0
    - _ReceiveShadows: 1
    - _Smoothness: 0
    - _SmoothnessTextureChannel: 0
    - _SpecularHighlights: 1
    - _SrcBlend: 1
    - _SrcBlendAlpha: 1
    - _Surface: 0
    - _UVSec: 0
    - _V_WIRE_Ao: 0
    - _V_WIRE_AoStrength: 1
    - _V_WIRE_BumpEnumID: 0
    - _V_WIRE_DistanceFade: 0
    - _V_WIRE_DistanceFadeEnd: 10
    - _V_WIRE_DistanceFadeStart: 5
    - _V_WIRE_DynamicGI: 0
    - _V_WIRE_DynamicGIEnumID: 0
    - _V_WIRE_DynamicGItrength: 0
    - _V_WIRE_DynamicMaskEffectsBaseTexEnumID: 0
    - _V_WIRE_DynamicMaskEffectsBaseTexInvert: 0
    - _V_WIRE_DynamicMaskEnumID: 0
    - _V_WIRE_DynamicMaskInvert: -1
    - _V_WIRE_DynamicMaskSmooth: 1
    - _V_WIRE_DynamicMaskType: 0
    - _V_WIRE_EmissionStrength: 0
    - _V_WIRE_FixedSize: 0
    - _V_WIRE_FresnelBias: 0
    - _V_WIRE_FresnelEnumID: 0
    - _V_WIRE_FresnelInvert: 0
    - _V_WIRE_FresnelPow: 1
    - _V_WIRE_IncludeLightEnumID: 0
    - _V_WIRE_NormalScale: 1
    - _V_WIRE_RenderingOptions_PBREnumID: 0
    - _V_WIRE_Size: 1
    - _V_WIRE_Source_Options: 0
    - _V_WIRE_Tag: 0
    - _V_WIRE_Title_M_Options: 0
    - _V_WIRE_Title_S_Options: 0
    - _V_WIRE_Title_V_Options: 0
    - _V_WIRE_Title_W_Options: 0
    - _V_WIRE_TransparencyEnumID: 0
    - _V_WIRE_Transparency_M_Options: 0
    - _V_WIRE_TransparentTex_Alpha_Offset: 0
    - _V_WIRE_TransparentTex_Invert: 0
    - _V_WIRE_VertexColor: 0
    - _V_WIRE_WireTex_UVSet: 0
    - _V_WIRE_WireVertexColor: 0
    - _WorkflowMode: 1
    - _ZWrite: 1
    m_Colors:
    - Color_DF059B30: {r: 0.46274513, g: 0.46274513, b: 0.46274513, a: 1}
    - _BaseColor: {r: 0.6981132, g: 0.6981132, b: 0.6981132, a: 1}
    - _Color: {r: 0.6981132, g: 0.6981132, b: 0.6981132, a: 1}
    - _EmissionColor: {r: 0, g: 0, b: 0, a: 1}
    - _SpecColor: {r: 0.122641504, g: 0.122641504, b: 0.122641504, a: 1}
    - _V_WIRE_Color: {r: 0, g: 0, b: 0, a: 0}
    - _V_WIRE_MainTex_Scroll: {r: 0, g: 0, b: 0, a: 0}
    - _V_WIRE_ObjectWorldPos: {r: 0, g: 0, b: 0, a: 0}
    - _V_WIRE_SourceTex_Scroll: {r: 0, g: 0, b: 0, a: 0}
    - _V_WIRE_WireTex_Scroll: {r: 0, g: 0, b: 0, a: 0}
  m_BuildTextureStacks: []
  m_AllowLocking: 1
