%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!21 &2100000
Material:
  serializedVersion: 8
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_Name: Flipped Normals Galaxy Variant for Tetrahedron
  m_Shader: {fileID: -6465566751694194690, guid: 3b64b5de3bfb8a949963df728aa306c0, type: 3}
  m_Parent: {fileID: 0}
  m_ModifiedSerializedProperties: 0
  m_ValidKeywords:
  - _RIMENABLED_ON
  - _RIMNOISECAENABLED_ON
  m_InvalidKeywords:
  - _EMISSION
  m_LightmapFlags: 2
  m_EnableInstancingVariants: 1
  m_DoubleSidedGI: 0
  m_CustomRenderQueue: -1
  stringTagMap: {}
  disabledShaderPasses:
  - MOTIONVECTORS
  m_LockedProperties: 
  m_SavedProperties:
    serializedVersion: 3
    m_TexEnvs:
    - _BumpMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _CloudsRamp:
        m_Texture: {fileID: 2800000, guid: 9f28f074599d7354f819614ab36087c2, type: 3}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _CloudsTexture:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _CometsTexture:
        m_Texture: {fileID: 8900000, guid: d18f54fc546873440ac8e9582ce0b4d6, type: 3}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _DarkCloudsTexture:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _DetailAlbedoMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _DetailMask:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _DetailNormalMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _EmissionMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _GalaxyTexture:
        m_Texture: {fileID: 2800000, guid: a2960ffde020f27409e070d92fb2e00b, type: 3}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _MainTex:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _MetallicGlossMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _NormalTexture:
        m_Texture: {fileID: 2800000, guid: a6f4682ed91450f4fa30f4f386b2a64d, type: 3}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _OcclusionMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _ParallaxMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _RimNoiseDistortionTexture:
        m_Texture: {fileID: 2800000, guid: 7d5f6337ffe5dfa4c99c39298646caae, type: 3}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _RimNoiseTexture:
        m_Texture: {fileID: 2800000, guid: 57535fb8629b4c6eb8530d7d3664d59f, type: 3}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _RimRamp:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _SineBot_Normal:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _StarsTexture:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _SunDistortionTexture:
        m_Texture: {fileID: 2800000, guid: e0973bba0d1ef6f4f9d433ea19783ab0, type: 3}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _SunRamp:
        m_Texture: {fileID: 2800000, guid: 256d86d8496a4e0f947100121f1fafb2, type: 3}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _SunTest:
        m_Texture: {fileID: 2800000, guid: 8cd4d56f7d8bae94e8e8b3081e1fcd89, type: 3}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _SunTexture:
        m_Texture: {fileID: 2800000, guid: 8cd4d56f7d8bae94e8e8b3081e1fcd89, type: 3}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _TextureSample0:
        m_Texture: {fileID: 2800000, guid: 739a0ed670307844a812b1b032ea60e1, type: 3}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _TextureSample11:
        m_Texture: {fileID: 2800000, guid: c6c0eeac2acf4b87b4f6a3e437fc1de0, type: 3}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _TextureSample13:
        m_Texture: {fileID: 2800000, guid: b17ee7d7b3064e71a219a093502d5193, type: 3}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _TextureSample3:
        m_Texture: {fileID: 2800000, guid: 57da1409b06446499f488b2cc631b382, type: 3}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _TextureSample4:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _TextureSample7:
        m_Texture: {fileID: 2800000, guid: e8276d11db3e4d7ea7b22c786790115e, type: 3}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _TextureSample8:
        m_Texture: {fileID: 2800000, guid: e0973bba0d1ef6f4f9d433ea19783ab0, type: 3}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _TopTexture0:
        m_Texture: {fileID: 2800000, guid: b74ab2df0efa4587a2684ac5201c036f, type: 3}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _kisspngspaceskyboxtexturemappingcubemappingnightsky5ac07d8380e2621165275515225644835279:
        m_Texture: {fileID: 8900000, guid: 43b272ebb5485644f9edd04a1c827a8f, type: 3}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _texcoord:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - unity_Lightmaps:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - unity_LightmapsInd:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - unity_ShadowMasks:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    m_Ints: []
    m_Floats:
    - _554: 9.86
    - _A1: 0
    - _AlphaCutoff: 0.5
    - _BumpScale: 1
    - _ChomaticAberration: 0
    - _CloudOpacityExp: 2
    - _CloudsEmissionPower: 10
    - _CloudsOpacityExp: 1.5
    - _CloudsOpacityPower: 1
    - _CloudsRampOffsetExp: 0.85
    - _CloudsRampOffsetExp2: 4
    - _CloudsRampOffsetMultiply: 1
    - _CloudsRampTilingMultiply: 1
    - _CloudsRotationSpeed: -0.04
    - _CometsDistortionAmount: 1
    - _CometsRotationSpeed: -0.1
    - _Cutoff: 0.5
    - _DARKCLOUDSENABLED: 0
    - _DarkCloudsEdgesGlowClamp: 4
    - _DarkCloudsEdgesGlowExp: 1
    - _DarkCloudsEdgesGlowGB: 0
    - _DarkCloudsEdgesGlowPower: 10
    - _DarkCloudsEdgesGlowStyle: 0
    - _DarkCloudsLighten: 10
    - _DarkCloudsRotationSpeed: -0.06
    - _DarkCloudsThicker: 1
    - _DetailNormalMapScale: 1
    - _DistortionAmount: 0.5
    - _DistortionExp: 1
    - _DistortionExp2: 1
    - _DstBlend: 0
    - _Eta: -0.1
    - _EtaAAEdgesFix: 0.25
    - _EtaEdgesFix: 0
    - _EtaFresnelExp: 3
    - _EtaFresnelExp2: 1
    - _Exp: 4
    - _FinalPower: 0.6
    - _FixMaybe: 0
    - _Float0: -1
    - _Float0df: 0.5
    - _Float1: 1
    - _Float12: 0.54
    - _Float13: 20
    - _Float14: 2
    - _Float15: 6
    - _Float2: 8
    - _Float28: 10
    - _Float29: -0.25
    - _Float3: 1
    - _Float30: 6
    - _Float33: 6
    - _Float34: 0.5
    - _Float35: 6
    - _Float36: 20
    - _Float37: 0.75
    - _Float4: -0.75
    - _Float5: 0.5
    - _Float6: 1
    - _Float7: -0.5
    - _Float8: 1
    - _Float9: 2
    - _FresnelGlowExp: 4
    - _FresnelGlowExp2: 2
    - _FresnelGlowPower: 2
    - _FresnelMaskExp: 4
    - _FresnelMaskExp2: 4
    - _FresnelModeAddOrMultiply: 0
    - _GalaxyRotationSpeed: 0
    - _GalaxyTilingU: 1
    - _GalaxyTilingV: 1
    - _GlossMapScale: 1
    - _Glossiness: 0.5
    - _GlossyReflections: 1
    - _IOR: 1.5
    - _Keyword0: 1
    - _Metallic: 0
    - _Mode: 0
    - _NormalAmount: 1
    - _NormalSpherize: 0
    - _OcclusionStrength: 1
    - _Parallax: 0.02
    - _QueueControl: 0
    - _QueueOffset: 0
    - _RIMENABLED: 1
    - _RIMNOISECAENABLED: 1
    - _RefractionCA: 0
    - _RefractionExp: 1
    - _RefractionExp2: 1
    - _RefractionIOR: 1.5
    - _RefrectionCASecondMode: 1
    - _RimAddOrMultiply: 1
    - _RimEmissionPower: 2
    - _RimExp: 3
    - _RimExp2: 2
    - _RimNoiseAmount: 0.75
    - _RimNoiseCAAmount: 0.05
    - _RimNoiseCARimMaskExp: 4.06
    - _RimNoiseCAU: 1
    - _RimNoiseCAV: 0
    - _RimNoiseDistortionAmount: 0.5
    - _RimNoiseDistortionScaleU: 2
    - _RimNoiseDistortionScaleV: 4
    - _RimNoiseDistortionScrollSpeed: 0.05
    - _RimNoiseDistortionTilingU: 2
    - _RimNoiseDistortionTilingV: 4
    - _RimNoiseNormalSpherize: 0
    - _RimNoiseRefraction: 0.5
    - _RimNoiseScrollSpeed: 0.025
    - _RimNoiseSpherize: 0
    - _RimNoiseTiling: 0.1
    - _RimNoiseTilingU: 0.1
    - _RimNoiseTilingV: 1
    - _RimNoiseToRefraction: 0
    - _RimNoiseTwistAmount: 0.25
    - _RimRampOffsetExp2: 1
    - _RotationClouds: 0
    - _RotationDarkClouds: 0
    - _RotationStars: 0
    - _Scale: 1
    - _SmoothnessTextureChannel: 0
    - _SpecularHighlights: 1
    - _SphericalDistortionAmount: 0
    - _SphericalDistortionExp: 1
    - _SrcBlend: 1
    - _StarsEmissionPower: 2.5
    - _StarsHueShift: 0
    - _StarsRotationSpeed: -0.02
    - _SunDistortionPower: -0.25
    - _SunDistortionScrollSpeed: -0.75
    - _SunDistortionTilingU: 3
    - _SunDistortionTilingV: 0.1
    - _SunEnabled: 0
    - _SunIOR: 0.5
    - _SunOpacityPower: 1
    - _SunPower: 7.5
    - _SunRampEnabled: 0
    - _SunRampOffsetExp2: 1
    - _SunRefractionExp: 1
    - _SunRefractionExp2: 1
    - _SunRefractionIOR: 0.5
    - _SunScale: 1
    - _TestIOR: 0.5
    - _TestPower: 7.5
    - _ToggleSwitch0: 0
    - _UVSec: 0
    - _ZWrite: 1
    - __dirty: 0
    m_Colors:
    - _CloudsRampColorTint: {r: 1, g: 1, b: 1, a: 1}
    - _Color: {r: 1, g: 1, b: 1, a: 1}
    - _Color0: {r: 1, g: 0, b: 0, a: 1}
    - _EmissionColor: {r: 0, g: 0, b: 0, a: 1}
    - _FresnelGlowColor: {r: 1, g: 1, b: 1, a: 1}
    - _RimColor: {r: 1, g: 1, b: 1, a: 1}
    - _RimEmissionColor: {r: 1, g: 1, b: 1, a: 1}
    - _RimNoiseSpherizeCenter: {r: 0, g: 0, b: 0, a: 0}
    - _RimNoiseSpherizePosition: {r: 0, g: 0, b: 0, a: 0}
    - _RotationAxis: {r: 0, g: 1, b: 0, a: 0}
    - _SunColor: {r: 1, g: 0, b: 0, a: 1}
    - _SunLocalPosition: {r: 0, g: 0, b: 0, a: 0}
    - _SunRampColorTint: {r: 1, g: 1, b: 1, a: 1}
    - _TestColor: {r: 1, g: 0, b: 0, a: 1}
    - _Vector0: {r: 2, g: 4, b: 0, a: 0}
    - _Vector2: {r: 0, g: 0, b: 0, a: 0}
    - _Vector4: {r: 0, g: 0, b: 0, a: 0}
  m_BuildTextureStacks: []
  m_AllowLocking: 1
--- !u!114 &3617053934558282015
MonoBehaviour:
  m_ObjectHideFlags: 11
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: d0353a89b1f911e48b9e16bdc9f2e058, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  version: 10
