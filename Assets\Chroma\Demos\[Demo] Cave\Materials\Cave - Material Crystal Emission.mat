%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!114 &-8855037555998651288
MonoBehaviour:
  m_ObjectHideFlags: 11
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 639247ca83abc874e893eb93af2b5e44, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  version: 0
--- !u!28 &-5040638340820670199
Texture2D:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_Name: z__HeightGradientTextureTex{"mode":1,"colorKeys":[{"color":{"r":1.0,"g":1.0,"b":1.0,"a":1.0},"time":0.0},{"color":{"r":1.0,"g":1.0,"b":1.0,"a":1.0},"time":1.0}],"alphaKeys":[{"alpha":1.0,"time":0.0},{"alpha":1.0,"time":1.0}]}
  m_ImageContentsHash:
    serializedVersion: 2
    Hash: 00000000000000000000000000000000
  m_IsAlphaChannelOptional: 0
  serializedVersion: 4
  m_Width: 256
  m_Height: 1
  m_CompleteImageSize: 1024
  m_MipsStripped: 0
  m_TextureFormat: 5
  m_MipCount: 1
  m_IsReadable: 1
  m_IsPreProcessed: 0
  m_IgnoreMipmapLimit: 0
  m_MipmapLimitGroupName: 
  m_StreamingMipmaps: 0
  m_StreamingMipmapsPriority: 0
  m_VTOnly: 0
  m_AlphaIsTransparency: 0
  m_ImageCount: 1
  m_TextureDimension: 2
  m_TextureSettings:
    serializedVersion: 2
    m_FilterMode: 0
    m_Aniso: 1
    m_MipBias: 0
    m_WrapU: 1
    m_WrapV: 1
    m_WrapW: 1
  m_LightmapFormat: 0
  m_ColorSpace: 1
  m_PlatformBlob: 
  image data: 1024
  _typelessdata: ffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffff
  m_StreamData:
    serializedVersion: 2
    offset: 0
    size: 0
    path: 
--- !u!28 &-1044916443142072157
Texture2D:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_Name: z__ShadingGradientTextureTex{"mode":0,"colorKeys":[{"color":{"r":0.5568627715110779,"g":0.8627451062202454,"b":0.9921568632125855,"a":1.0},"time":0.0},{"color":{"r":0.7686274647712708,"g":0.9529411792755127,"b":0.9607843160629273,"a":1.0},"time":0.2980392277240753}],"alphaKeys":[{"alpha":1.0,"time":0.0},{"alpha":1.0,"time":1.0}]}
  m_ImageContentsHash:
    serializedVersion: 2
    Hash: 00000000000000000000000000000000
  m_IsAlphaChannelOptional: 0
  serializedVersion: 4
  m_Width: 1024
  m_Height: 1
  m_CompleteImageSize: 8192
  m_MipsStripped: 0
  m_TextureFormat: 17
  m_MipCount: 1
  m_IsReadable: 1
  m_IsPreProcessed: 0
  m_IgnoreMipmapLimit: 0
  m_MipmapLimitGroupName: 
  m_StreamingMipmaps: 0
  m_StreamingMipmapsPriority: 0
  m_VTOnly: 0
  m_AlphaIsTransparency: 0
  m_ImageCount: 1
  m_TextureDimension: 2
  m_TextureSettings:
    serializedVersion: 2
    m_FilterMode: 1
    m_Aniso: 1
    m_MipBias: 0
    m_WrapU: 1
    m_WrapV: 1
    m_WrapW: 1
  m_LightmapFormat: 0
  m_ColorSpace: 0
  m_PlatformBlob: 
  image data: 8192
  _typelessdata: 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
  m_StreamData:
    serializedVersion: 2
    offset: 0
    size: 0
    path: 
--- !u!21 &2100000
Material:
  serializedVersion: 8
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_Name: Cave - Material Crystal Emission
  m_Shader: {fileID: -6465566751694194690, guid: 4cf852656a995644e830df7333e1eb6e, type: 3}
  m_Parent: {fileID: 0}
  m_ModifiedSerializedProperties: 0
  m_ValidKeywords: []
  m_InvalidKeywords:
  - _BLEND_MODE_OVERWRITE
  - _DISABLE_SHADOWS
  - _EMISSION
  - _HEIGHT_MODE_OBJECT
  - _SPECULAR_SETUP
  - __T_HEIGHT_MODE_OBJECT
  m_LightmapFlags: 3
  m_EnableInstancingVariants: 0
  m_DoubleSidedGI: 0
  m_CustomRenderQueue: 2000
  stringTagMap: {}
  disabledShaderPasses:
  - MOTIONVECTORS
  m_LockedProperties: 
  m_SavedProperties:
    serializedVersion: 3
    m_TexEnvs:
    - _BaseMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _BaseTexture:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _BumpMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _DetailAlbedoMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _DetailMask:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _DetailNormalMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _EmissionMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _HeightGradientTexture:
        m_Texture: {fileID: -5040638340820670199}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _MainTex:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _MetallicGlossMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _OcclusionMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _ParallaxMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _ShadingGradientTexture:
        m_Texture: {fileID: -1044916443142072157}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _SpecGlossMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - unity_Lightmaps:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - unity_LightmapsInd:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - unity_ShadowMasks:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    m_Ints: []
    m_Floats:
    - _AlphaClip: 1
    - _BLEND_MODE: 0
    - _BUILTIN_QueueControl: -1
    - _BUILTIN_QueueOffset: 0
    - _Blend: 0
    - _BumpScale: 1
    - _CastShadows: 1
    - _ClearCoatMask: 0
    - _ClearCoatSmoothness: 0
    - _Cull: 2
    - _Cutoff: 0.5
    - _DISABLE_SHADOWS: 1
    - _DetailAlbedoMapScale: 1
    - _DetailNormalMapScale: 1
    - _DstBlend: 0
    - _ENABLE_HEIGHT_GRADIENT: 0
    - _EnvironmentReflections: 1
    - _GlossMapScale: 0
    - _Glossiness: 0
    - _GlossyReflections: 0
    - _HEIGHT_MODE: 0
    - _Metallic: 0
    - _OcclusionStrength: 1
    - _Opacity: 0.4
    - _Parallax: 0.005
    - _QueueControl: 0
    - _QueueOffset: 0
    - _RECEIVE_SHADOWS_OFF: 0
    - _ReceiveShadows: 0
    - _ShadowStrength: 1
    - _Smoothness: 0.5
    - _SmoothnessTextureChannel: 0
    - _SpecularHighlights: 1
    - _SrcBlend: 1
    - _Surface: 0
    - _WorkflowMode: 0
    - _ZTest: 4
    - _ZWrite: 1
    - _ZWriteControl: 0
    - __T_HEIGHT_MODE: 0
    m_Colors:
    - _BaseColor: {r: 0, g: 1, b: 0.91193295, a: 1}
    - _Color: {r: 1, g: 1, b: 1, a: 1}
    - _Emission: {r: 0, g: 0, b: 0, a: 0}
    - _EmissionColor: {r: 0.035153255, g: 1.0646409, b: 0.8759912, a: 1}
    - _HeightRange: {r: 0, g: 1, b: 0, a: 0}
    - _ShadowColor: {r: 0, g: 0, b: 0, a: 1}
    - _SpecColor: {r: 0.19999996, g: 0.19999996, b: 0.19999996, a: 1}
  m_BuildTextureStacks: []
  m_AllowLocking: 1
--- !u!114 &3213663874025035314
MonoBehaviour:
  m_ObjectHideFlags: 11
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: d0353a89b1f911e48b9e16bdc9f2e058, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  version: 10
