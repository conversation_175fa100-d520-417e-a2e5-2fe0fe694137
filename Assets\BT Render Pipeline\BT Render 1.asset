%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!114 &-9118587441483550813
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 360cfc57e9d2442419350b973e0c997e, type: 3}
  m_Name: CaptureColorFeature
  m_EditorClassIdentifier: 
  m_Active: 1
--- !u!114 &-7844175927201956791
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 6ee67e36f6958b74291994b4c2ba6007, type: 3}
  m_Name: OrderIndependentTransparencyRenderer
  m_EditorClassIdentifier: 
  m_Active: 1
--- !u!114 &-7311696734307070255
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 6b3d386ba5cd94485973aee1479b272e, type: 3}
  m_Name: Render On Top
  m_EditorClassIdentifier: 
  m_Active: 1
  settings:
    passTag: Render On Top
    Event: 550
    filterSettings:
      RenderQueueType: 0
      LayerMask:
        serializedVersion: 2
        m_Bits: 20608
      PassNames: []
    overrideMaterial: {fileID: 0}
    overrideMaterialPassIndex: 0
    overrideShader: {fileID: 0}
    overrideShaderPassIndex: 0
    overrideMode: 1
    overrideDepthState: 1
    depthCompareFunction: 8
    enableWrite: 0
    stencilSettings:
      overrideStencilState: 0
      stencilReference: 0
      stencilCompareFunction: 8
      passOperation: 0
      failOperation: 0
      zFailOperation: 0
    cameraSettings:
      overrideCamera: 0
      restoreCamera: 1
      offset: {x: 0, y: 0, z: 0, w: 0}
      cameraFieldOfView: 60
--- !u!114 &-6783378278466740356
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 55e6184d2ea958044a6434dca151c688, type: 3}
  m_Name: JPG
  m_EditorClassIdentifier: 
  m_Active: 1
  shader: {fileID: 0}
--- !u!114 &-5587902338659560508
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 473a86c9e274347dfbdde619584cebe9, type: 3}
  m_Name: HighlightPlusRenderPassFeature
  m_EditorClassIdentifier: 
  m_Active: 1
  renderPassEvent: 550
  clearStencil: 0
  previewInEditMode: 1
  showInPreviewCamera: 1
--- !u!114 &-3458786458444303034
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 6b3d386ba5cd94485973aee1479b272e, type: 3}
  m_Name: Render Feature  Outline Transparent
  m_EditorClassIdentifier: 
  m_Active: 1
  settings:
    passTag: Render Feature  Outline Transparent
    Event: 450
    filterSettings:
      RenderQueueType: 1
      LayerMask:
        serializedVersion: 2
        m_Bits: 4294967295
      PassNames:
      - OutlinePass
    overrideMaterial: {fileID: 0}
    overrideMaterialPassIndex: 0
    overrideShader: {fileID: 0}
    overrideShaderPassIndex: 0
    overrideMode: 1
    overrideDepthState: 0
    depthCompareFunction: 4
    enableWrite: 1
    stencilSettings:
      overrideStencilState: 0
      stencilReference: 0
      stencilCompareFunction: 8
      passOperation: 0
      failOperation: 0
      zFailOperation: 0
    cameraSettings:
      overrideCamera: 0
      restoreCamera: 1
      offset: {x: 0, y: 0, z: 0, w: 0}
      cameraFieldOfView: 60
--- !u!114 &-1951979159481492658
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 73fb227e61eeb3b42bcecf46ee71761d, type: 3}
  m_Name: ColorTextureFeature
  m_EditorClassIdentifier: 
  m_Active: 1
--- !u!114 &-913699881706904713
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 95aab45d7b9933848aa6e8800c1eb8f8, type: 3}
  m_Name: CaptureMotionFeature
  m_EditorClassIdentifier: 
  m_Active: 1
--- !u!114 &11400000
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: de640fe3d0db1804a85f9fc8f5cadab6, type: 3}
  m_Name: BT Render 1
  m_EditorClassIdentifier: 
  debugShaders:
    debugReplacementPS: {fileID: 4800000, guid: cf852408f2e174538bcd9b7fda1c5ae7, type: 3}
    hdrDebugViewPS: {fileID: 4800000, guid: 573620ae32aec764abd4d728906d2587, type: 3}
    probeVolumeSamplingDebugComputeShader: {fileID: 7200000, guid: 53626a513ea68ce47b59dc1299fe3959, type: 3}
  probeVolumeResources:
    probeVolumeDebugShader: {fileID: 0}
    probeVolumeFragmentationDebugShader: {fileID: 0}
    probeVolumeOffsetDebugShader: {fileID: 0}
    probeVolumeSamplingDebugShader: {fileID: 0}
    probeSamplingDebugMesh: {fileID: 0}
    probeSamplingDebugTexture: {fileID: 0}
    probeVolumeBlendStatesCS: {fileID: 0}
  m_RendererFeatures:
  - {fileID: 3516313377169516816}
  - {fileID: -7844175927201956791}
  - {fileID: 3594331921476673168}
  - {fileID: -5587902338659560508}
  - {fileID: -7311696734307070255}
  - {fileID: 1536031269342361760}
  - {fileID: -1951979159481492658}
  - {fileID: 7870361055296967411}
  - {fileID: 4297979556083498716}
  - {fileID: -3458786458444303034}
  - {fileID: 6630285163055302689}
  m_RendererFeatureMap: 10a924e56274cc3049fc74ef8de3239390de76c8d6a1e131c4539fd06dc873b2d14e3e268fa2879aa0380b1d4d1451154edb23e64b2de9e4f3520b4bae23396ddc7e3f74a27da53b46f57e9e0aecffcf2148741f3a81035c
  m_UseNativeRenderPass: 0
  xrSystemData: {fileID: 0}
  postProcessData: {fileID: 11400000, guid: 41439944d30ece34e96484bdb6645b55, type: 2}
  m_AssetVersion: 2
  m_OpaqueLayerMask:
    serializedVersion: 2
    m_Bits: 4294967295
  m_TransparentLayerMask:
    serializedVersion: 2
    m_Bits: 4294967295
  m_DefaultStencilState:
    overrideStencilState: 0
    stencilReference: 0
    stencilCompareFunction: 8
    passOperation: 2
    failOperation: 0
    zFailOperation: 0
  m_ShadowTransparentReceive: 1
  m_RenderingMode: 2
  m_DepthPrimingMode: 1
  m_CopyDepthMode: 0
  m_DepthAttachmentFormat: 0
  m_DepthTextureFormat: 0
  m_AccurateGbufferNormals: 0
  m_IntermediateTextureMode: 1
--- !u!114 &1077562940319648537
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 55e6184d2ea958044a6434dca151c688, type: 3}
  m_Name: JPG
  m_EditorClassIdentifier: 
  m_Active: 1
  shader: {fileID: 0}
--- !u!114 &1536031269342361760
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 729a2e952309e2547a3747b2cdd9a36f, type: 3}
  m_Name: Shapes Render Feature
  m_EditorClassIdentifier: 
  m_Active: 1
--- !u!114 &3402876150238971299
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 0b6eb1f753664124dbc9f6907ce1fa0b, type: 3}
  m_Name: DatamoshFeature
  m_EditorClassIdentifier: 
  m_Active: 1
  settings:
    intensity: 0.87
    blendAmount: 0.386
    updateRate: 2.4
    updateOnEveryFrame: 1
  datamoshShader: {fileID: -6465566751694194690, guid: 6c220cecbddfce040bf745312977bb16, type: 3}
--- !u!114 &3516313377169516816
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 8f6dc72684698be4ab2c5d4bb40db23d, type: 3}
  m_Name: BFIRendererFeature
  m_EditorClassIdentifier: 
  m_Active: 0
--- !u!114 &3594331921476673168
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 96dab975e69883249a56554e791c4fd0, type: 3}
  m_Name: ButoRenderFeature
  m_EditorClassIdentifier: 
  m_Active: 1
  settings:
    renderPassEvent: 450
--- !u!114 &4297979556083498716
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 6b3d386ba5cd94485973aee1479b272e, type: 3}
  m_Name: Render Feature - Outline Opaque
  m_EditorClassIdentifier: 
  m_Active: 1
  settings:
    passTag: Render Feature  Outline
    Event: 300
    filterSettings:
      RenderQueueType: 0
      LayerMask:
        serializedVersion: 2
        m_Bits: 4294967295
      PassNames:
      - OutlinePass
    overrideMaterial: {fileID: 0}
    overrideMaterialPassIndex: 0
    overrideShader: {fileID: 0}
    overrideShaderPassIndex: 0
    overrideMode: 1
    overrideDepthState: 0
    depthCompareFunction: 4
    enableWrite: 1
    stencilSettings:
      overrideStencilState: 0
      stencilReference: 0
      stencilCompareFunction: 8
      passOperation: 0
      failOperation: 0
      zFailOperation: 0
    cameraSettings:
      overrideCamera: 0
      restoreCamera: 1
      offset: {x: 0, y: 0, z: 0, w: 0}
      cameraFieldOfView: 60
--- !u!114 &6630285163055302689
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 629a8b5c32be1b3498dbbc2cb9f6f46d, type: 3}
  m_Name: Flux
  m_EditorClassIdentifier: 
  m_Active: 1
  shader: {fileID: 4800000, guid: a10ddf3c9f9118443ae3397a080ef480, type: 3}
  useComputeShaders: 0
  fluxComputeShader: {fileID: 7200000, guid: 317587a1c3823784389b5f571f5da68a, type: 3}
--- !u!114 &7870361055296967411
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: bb9081ca8e4c7ba4c9dce4cf09686abc, type: 3}
  m_Name: MotionTextureFeature
  m_EditorClassIdentifier: 
  m_Active: 1
